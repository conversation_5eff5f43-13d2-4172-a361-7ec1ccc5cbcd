.detail-product-card-wrapper
{
    display: flex;
    height: 23.5rem;
    padding-right: 2.25rem;
    align-items: center;
    gap: 3.75rem;
    align-self: stretch;

    border-radius: 0.75rem;
    background: var(--sbds-accent-2-color, #00164E);
    overflow: hidden;
}

.detail-product-card-image
{
    flex: 1 0 0;
    align-self: stretch;
    width: 37rem;
}

.detail-product-card-image > img
{
    width: 100%;
}

.detail-product-card-cta
{
    display: flex;
    padding: 2rem 0rem;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
}

.detail-product-card-cta-heading
{

}

.detail-product-card-cta-heading > span
{
    color: var(--sbds-white-color, #ffffff);
    font-size: 2rem;
    font-style: normal;
    font-weight: 500;
    line-height: 130%;
}

.detail-product-card-cta-description
{
    align-self: stretch;
}

.detail-product-card-cta-description > p
{
    color: var(--sbds-white-color, #ffffff);
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: 130%;
}

.detail-product-card-cta-button
{

}

.detail-product-card-cta-button > button
{
    display: flex;
    height: 3.125rem;
    padding: 1.25rem 5.125rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    border-radius: 0.5rem;
    background: var(--sbds-secondary-color, #0051FF);
    color: var(--sbds-white-color, #ffffff);
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 700;
    line-height: 1rem;
    text-transform: uppercase;
    border: none;
}

.detail-product-card-cta-button > button:hover
{
    background-color: #44a8ff;
}