.splash-page-wrapper
{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4rem;
    width: 80rem;
    justify-content: flex-start;
    justify-self: center;
    position: relative;
    top: 63.58px;
    margin-bottom: 7rem;
}

.splash-page-header-background
{
    display: flex; 
    width: 100%;
    height: 356px;
    flex-direction: column;
    align-items: flex-start;
    flex-shrink: 0;
    background: #003FCA;
    position: absolute;
    top: 0px;
    left: 0;
    overflow: hidden;
}

.splash-page-header-background > img
{
    width: 820px;
    position: relative;
    top: -280px;
    left: -240px;
}

.splash-page-header
{
    width: 100%;
}

.splash-page-value-prop
{

}

.splash-page-cta-section
{
    width: 100%;
}

.splash-page-cta-section c-osbapi-api-catalogue
{
    width: 100%;
}

.splash-pagetab-section
{
    
}

@media screen and (min-width: 90rem)
{
    .splash-page-cta-section
    {
        display: flex;
        gap: 24px;
        width: 100%;
    }

    .splash-page-cta-section > c-osbapi-api-catalogue
    {
        display: flex;
        flex-grow: 1;
        width: 100%;
    }

    .splash-page-cta-section > c-osb-add-ons
    {
        display: flex;
        flex-grow: 1;
    }
}