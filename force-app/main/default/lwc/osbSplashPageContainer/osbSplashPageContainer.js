import { api, LightningElement, wire, track } from 'lwc';
import { CurrentPageReference } from 'lightning/navigation';
import OSB_PageBackgroundImage from '@salesforce/resourceUrl/OSB_PageBackgroundImage';
import getAnypointPage from '@salesforce/apex/OSB_OD_ProductDetails_CTRL.getAnypointPage';
import getMultipleAnypointPages from '@salesforce/apex/OSB_OD_ProductDetails_CTRL.getMultipleAnypointPages';
import getCommunityAssetVersion from '@salesforce/apex/OSB_OD_ProductDetails_CTRL.getCommunityAssetVersion';

export default class OsbSplashPageContainer extends LightningElement
{
    @api recordId;
    @api apiHeading = "Some of the benefits";
    @api icons;

    @track showGetInTouchModal = false;

    headerBackground = OSB_PageBackgroundImage;

    communityAssetVersionId;

    benefits = [];
    apiSubheading;
    image;

    documentationHeading;
    documentationDescription;
    documentationButtonLabel;

    ctaHeading;
    ctaDescription;
    ctaButtonLabel;

    tabInfo;

    pageContent;
    isImage;
    html;
    attachmentPoint;

    @wire(CurrentPageReference)
    getStateParameters(currentPageReference)
    {
        if (currentPageReference) {
            this.apiHeading = currentPageReference.state.heading;
        }
    }

    @wire(getCommunityAssetVersion, { assetId: '$recordId' })
    WiredGetCommunityAssetVersion({ data, error }) {
        if (data)
        {
            this.communityAssetVersionId = data.Id;
            console.log('AssetId: ', JSON.stringify(this.communityAssetVersionId));
            
        }
        else if(error)
        {
            console.log('Parent id Error: ', JSON.stringify(error));
            
        }
    }

    get headerBackground()
    {
        return headerBackground;
    }

    get apiImage()
    {
        if (!isPromise(this.image) )
        {
            return this.image;
        }
    }

    get apiHeadings()
    {
        return this.apiHeading
    }

    get benefitsList()
    {
        console.log('in get: ',JSON.stringify(this.benefits));
        
        return this.benefits;
    }

    get productOfferingHeading()
    {
        return "API";
    }

    get productOfferingDescription()
    {
        return this.apiSubheading;
    }

    get tabObject()
    {
        return this.tabInfo;
    }

    connectedCallback()
    {

        if (this.recordId)
        {
            this.fetchImage();
            this.fetchSubHeading();
            this.fetchBenefits();
            this.fetchProductOffering();
            this.fetCta();
            this.fetchHowToGetIt();
        }
    }

    // @wire(getAnypointPage, { recordId: '$recordId', pageName: '$pageName' })
    // WiredGetAnypointPage({ data, error })
    // {
    //     if (data)
    //     {
    //         this.pageContent = data;
    //         console.log('wire content: ', this.pageContent);
            
    //         this.isImage = this.pageName === 'Banner Image';
    //         this.initializePage(this.pageContent);
    //     }
    //     else if (error)
    //     {
    //         console.log('error: ', JSON.stringify(error));
    //     }
        
    // }

    renderedCallback()
    {
        this.attachmentPoint = this.template.querySelector(
            'div[ishtmlcontainer=true]'
        );
    }

    fetchImage()
    {
        const pageName = "Banner Image";

        getAnypointPage({recordId: this.recordId, pageName: pageName})
        .then(imageResults => {
            console.log('image: ', JSON.stringify(imageResults));
            return this.convertLinkToImage(imageResults);
        })
        .then(convertedImage => {
            this.image = convertedImage;
        })
        .catch(error => {
            console.log('error: ', JSON.stringify(error));
        });
    }

    fetchSubHeading()
    {
        const pageName = "Overview";

        getAnypointPage({recordId: this.recordId, pageName: pageName})
        .then(subHeadingResults => {
            console.log('Sub-heading: ', JSON.stringify(subHeadingResults));
            this.apiSubheading = subHeadingResults;
        })
        .catch(error => {
            console.log('error: ', JSON.stringify(error));            
        });
    }

    fetchBenefits()
    {
        const pageNames = ["Benefit 1", "Benefit 2", "Benefit 3", "Benefit 4"];
        const count = 4;
        
        getMultipleAnypointPages({recordId: this.recordId, pageNames: pageNames})
        .then(benefitsRawResults => {
            console.log('Benefits raw: ', JSON.stringify(benefitsRawResults));
            return this.cleanUpList(benefitsRawResults)
        })
        .then(cleanBenefits => {
            console.log('clean: ', JSON.stringify(cleanBenefits));
            this.benefits = cleanBenefits;
        })
        .catch(error => {
            console.log('error: ', JSON.stringify(error));
        });
    }

    fetchProductOffering()
    {
        this.documentationHeading = "API";
        this.documentationDescription = this.apiSubheading;
        this.documentationButtonLabel = "Veiw Documentation";
    }

    fetCta()
    {
        this.ctaHeading = "Do you need help?";
        this.ctaDescription = "One of our guides will help you every step of the way!";
        this.ctaButtonLabel = "Get in Touch";
    }

    fetchHowToGetIt()
    {
        const pageName = "How to Get it";

        getAnypointPage({recordId: this.recordId, pageName: pageName})
        .then(HowToResults => {
            console.log('How to Get it: ', JSON.stringify(HowToResults));
            return this.parseMarkdownContent(HowToResults);
        })
        .then(cleanResults => {
            console.log('parsed: ', JSON.stringify(cleanResults));
            this.tabInfo = cleanResults;
        })
        .catch(error => {
            console.log('error: ', JSON.stringify(error));
        });
    }

    cleanUpList(rawList)
    {
        const themes = ['light', 'dark'];
        const icons = [
            'icon-icn_hourglass',
            'icon-icn_people_1_coins',
            'icon-icn_goal',
            'icon-icn_bulb'
        ];
    
        return rawList.map((entry, index) => {
            const cleaned = entry.replace(/^#\s*/, '').split('\n\n');
            const title = cleaned[0].replace(/\*\*/g, '').trim();
            const description = cleaned[1] ? cleaned[1].trim() : '';
    
            return {
                icon: icons[index % icons.length],
                title,
                description,
                theme: themes[index % themes.length]
            };
        });
    }

    parseMarkdownContent(rawText) {
        const result = {
            headings: [],
            descriptions: []
        };
    
        // Split content into sections by heading marker
        const sections = rawText.split('## **').filter(Boolean);
    
        sections.forEach(section => {
            const headingMatch = section.match(/^(.*?)\*\*/); // Extract heading
            const heading = headingMatch ? headingMatch[1].trim() : '';
    
            const description = section
                .replace(/^(.*?)\*\*/, '') // Remove heading portion
                .replace(/[-]{2,}/g, '')   // Remove horizontal lines
                .replace(/<br\s*\/?>/gi, ' ') // Replace <br> with space
                .replace(/\n+/g, ' ')      // Remove line breaks
                .replace(/\s+/g, ' ')      // Normalize multiple spaces
                .trim();
    
            if (heading) {
                result.headings.push(heading);
                result.descriptions.push(description);
            }
        });
    
        return result;
    }
    

    cleanApiDocumentation(rawText) {
        const result = [];
        let id = 1;
    
        // Split the input into sections by heading marker "## **"
        const sections = rawText.split('## **').filter(Boolean);
    
        sections.forEach(section => {
            // Extract heading and description
            const headingMatch = section.match(/^(.*?)\*\*/);
            const heading = headingMatch ? headingMatch[1].trim() : null;
    
            // Get the remaining description text after the closing '**'
            let description = section.replace(/^(.*?)\*\*/, '').trim();
    
            // Clean the description:
            // - Remove horizontal rules and <br> tags
            // - Replace multiple newlines with space
            description = description
                .replace(/[-]{2,}/g, '')            // remove horizontal rules (----)
                .replace(/<br\s*\/?>/gi, ' ')       // replace <br> with space
                .replace(/\n+/g, ' ')               // replace newlines with space
                .replace(/\s+/g, ' ')               // normalize multiple spaces
                .trim();
    
            if (heading && description) {
                result.push({
                    id: id++,
                    heading,
                    description
                });
            }
        });
    
        return result;
    }
    
    extractHeadings(rawText) {
        const headings = [];
    
        // Split into sections using heading marker
        const sections = rawText.split('## **').filter(Boolean);
    
        sections.forEach(section => {
            const match = section.match(/^(.*?)\*\*/);
            if (match && match[1]) {
                headings.push(match[1].trim());
            }
        });
    
        return headings;
    }
    
    extractDescriptions(rawText) {
        const descriptions = [];
    
        // Split into sections using heading marker
        const sections = rawText.split('## **').filter(Boolean);
    
        sections.forEach(section => {
            // Remove the heading portion to isolate description
            const withoutHeading = section.replace(/^(.*?)\*\*/, '').trim();
    
            let cleanDescription = withoutHeading
                .replace(/[-]{2,}/g, '')            // remove horizontal rules
                .replace(/<br\s*\/?>/gi, ' ')       // replace <br> with space
                .replace(/\n+/g, ' ')               // replace newlines with space
                .replace(/\s+/g, ' ')               // normalize multiple spaces
                .trim();
    
            descriptions.push(cleanDescription);
        });
    
        return descriptions;
    }
    

    // async assignVariables(imageContent, overview)
    // {
    //     this.image = await this.convertLinkToImage(imageContent);
    //     this.apiSubheading = overview;
    // }

    async initializePage(pageContent)
    {
        if (this.isImage)
        {
            this.image = await this.convertLinkToImage(pageContent);
            // this.isLoading = false;
            console.log('image: ', this.image);
            
            return;
        }
        await this.convertMarkdownToHtml();
        // this.isLoading = false;
    }

    async convertMarkdownToHtml()
    {
        if (this.pageContent)
        {
            Promise.all([loadScript(this, showdown)]).then(() => {
                this.converter = new window.showdown.Converter({
                    tables: true
                });
                
                let html = this.converter.makeHtml(this.pageContent);

                if (this.attachmentPoint)
                {
                    this.attachmentPoint.innerHTML = html;
                }
            });
        }
    }

    async convertLinkToImage(data)
    {
        let tempArr = data.split('(');

        tempArr[1] = tempArr[1].slice(0, -1);

        return tempArr[1];
    }

    handleGetInTouchClick() {
        this.showGetInTouchModal = true;
    }

    handleCloseModal() {
        this.showGetInTouchModal = false;
    }

    handleModalSubmit(event) {
        const formData = event.detail;
        console.log('Form submitted:', formData);
        this.handleCloseModal();
    }
}