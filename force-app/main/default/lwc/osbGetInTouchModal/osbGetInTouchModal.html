<template>
    <template lwc:if={show}>
        <div class="modal-backdrop" onclick={handleBackdropClick}>
            <div class="get-in-touch-modal" onclick={handleModalClick}>
                <div class="modal-header">
                    <h2>Get in touch</h2>
                    <button class="close-button" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" size="x-small"></lightning-icon>
                    </button>
                </div>
                <div class="modal-content">
                    <p class="modal-description">
                        Once you submit this form, the API will be automatically added to your dashboard.
                    </p>

                    <form class="contact-form">
                        <op-input
                            lwc:external
                            label="Name and Surname"
                            placeholder="<PERSON>lam<PERSON>"
                            name="fullname"
                            required
                            value={formData.name}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <op-input
                            lwc:external
                            label="Email"
                            placeholder="<EMAIL>"
                            type="email"
                            name="email"
                            required
                            value={formData.email}
                            onupdatevalue={handleInputChange}
                        ></op-input>
                        
                        <op-input
                            lwc:external
                            label="Contact number"
                            placeholder="+27 724355032"
                            name="phone"
                            type="tel"
                            required
                            value={formData.phone}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <op-input
                            lwc:external
                            label="Solution name"
                            placeholder="Authentifi"
                            name="solution"
                            value={formData.solution}
                            onupdatevalue={handleInputChange}
                        ></op-input>

                        <div class="form-group">
                            <label for="requestType">Type of request</label>
                            <select
                                id="requestType"
                                onchange={handleInputChange}
                                data-field="requestType"
                            >
                                <option value="">Get a call back</option>
                                <option value="demo">Book a demo</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="contactMethod">Preferred contact method</label>
                            <select
                                id="contactMethod"
                                onchange={handleInputChange}
                                data-field="contactMethod"
                            >
                                <option value="">Select an option</option>
                                <option value="phone">Phone</option>
                                <option value="email">Email</option>
                                <option value="teams">Teams</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="messageInput">Tell us more about how you would like to use this solution</label>
                            <textarea
                                id="messageInput"
                                placeholder="Placeholder"
                                onchange={handleInputChange}
                                data-field="message"
                                rows="4"
                            >{formData.message}</textarea>
                        </div>

                        <op-checkbox
                            lwc:external
                            onupdatechecked={handleCheckboxChange}
                            name="privacyConsent"
                            checked={formData.privacyConsent}
                            >I acknowledge and consent to the processing of my personal information by Standard Bank and/or relevant third-party providers, in line with Standard Bank's Privacy Statement.
                        </op-checkbox>
                        
                        <op-button 
                            lwc:external 
                            kind="primary" 
                            onclick={handleSubmit}
                            disabled={isSubmitDisabled}>SUBMIT
                        </op-button>
                    </form>
                </div>
            </div>
        </div>
    </template>
</template>
