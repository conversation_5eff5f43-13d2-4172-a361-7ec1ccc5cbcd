/* Get in Touch Modal Styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: flex-end;
    align-items: stretch;
    z-index: 9999;
    padding: 0;
}

.get-in-touch-modal {
    background: white;
    border-radius: 16px;
    width: 400px;
    max-width: 90vw;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    margin: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px 8px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #222E37;
}

.close-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #222E37;
    border-radius: 4px;
}

.close-button:hover {
    background-color: #f5f5f5;
}

.modal-content {
    padding: 24px;
}

.modal-description {
    margin: 0 0 24px 0;
    color: #222E37;
    font-size: 14px;
    line-height: 1.4;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #222E37;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0176d3;
    box-shadow: 0 0 0 2px rgba(1, 118, 211, 0.1);
}

.form-group select {
    background-color: white;
    cursor: pointer;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .modal-backdrop {
        justify-content: center;
        align-items: center;
        padding: 10px;
    }

    .get-in-touch-modal {
        margin: 0;
        width: 100%;
        max-width: 400px;
        height: auto;
        max-height: 90vh;
        border-radius: 16px;
    }
}
