# OSB Get In Touch Modal Component

A reusable Lightning Web Component for displaying a "Get in Touch" modal form.

## Overview

This component provides a standardized modal interface for collecting contact information from users. It includes form validation, responsive design, and follows the user's preferred modal positioning (right-side, full-height).

## Features

- **Responsive Design**: Adapts to different screen sizes
- **Form Validation**: Built-in validation for required fields
- **Reusable**: Can be used across multiple components
- **Accessible**: Includes proper ARIA attributes and keyboard navigation
- **Customizable**: Supports initial form data and custom event handling

## Usage

### Basic Implementation

```html
<c-osb-get-in-touch-modal
    show={showModal}
    onclose={handleCloseModal}
    onsubmit={handleModalSubmit}>
</c-osb-get-in-touch-modal>
```

### With Initial Form Data

```html
<c-osb-get-in-touch-modal
    show={showModal}
    initial-form-data={initialData}
    onclose={handleCloseModal}
    onsubmit={handleModalSubmit}>
</c-osb-get-in-touch-modal>
```

## Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `show` | Boolean | `false` | Controls modal visibility |
| `initialFormData` | Object | `{}` | Pre-populate form fields |

## Events

### `close`
Fired when the modal is closed (via close button or backdrop click).

```javascript
handleCloseModal() {
    this.showModal = false;
}
```

### `submit`
Fired when the form is successfully submitted with valid data.

**Event Detail**: Form data object containing all field values.

```javascript
handleModalSubmit(event) {
    const formData = event.detail;
    console.log('Form submitted:', formData);
    
    // Process the form data
    // Close the modal
    this.showModal = false;
}
```

## Form Fields

The modal includes the following form fields:

- **Name and Surname** (required)
- **Email** (required, email validation)
- **Contact Number** (required, tel format)
- **Solution Name** (optional)
- **Type of Request** (required, dropdown)
- **Preferred Contact Method** (required, dropdown)
- **Message** (optional, textarea)
- **Privacy Consent** (required, checkbox)

## Form Data Structure

```javascript
{
    name: '',
    email: '',
    phone: '',
    solution: '',
    requestType: '', // 'demo', 'other', or ''
    contactMethod: '', // 'phone', 'email', 'teams', or ''
    message: '',
    privacyConsent: false
}
```

## Public Methods

### `setFormData(data)`
Programmatically set form field values.

```javascript
const modal = this.template.querySelector('c-osb-get-in-touch-modal');
modal.setFormData({
    name: 'John Doe',
    email: '<EMAIL>'
});
```

### `getFormData()`
Retrieve current form field values.

```javascript
const modal = this.template.querySelector('c-osb-get-in-touch-modal');
const currentData = modal.getFormData();
```

## Styling

The component uses CSS custom properties and follows the existing design system. The modal:

- Positions on the right side of the screen
- Takes full height with rounded left edges
- Uses a semi-transparent backdrop
- Includes hover effects and focus states

## Dependencies

- Lightning Web Components framework
- External `op-input`, `op-button`, and `op-checkbox` components
- Lightning Design System icons

## Browser Support

Compatible with all modern browsers supported by the Lightning Platform.
