import { api, LightningElement, track } from 'lwc';

export default class OsbGetInTouchModal extends LightningElement {
    @api show = false;
    @api initialFormData;

    @track formData = {
        name: '',
        email: '',
        phone: '',
        solution: '',
        requestType: '',
        contactMethod: '',
        message: '',
        privacyConsent: false
    };

    connectedCallback() {
        // Initialize form data with any provided initial values
        if (this.initialFormData) {
            this.formData = { ...this.formData, ...this.initialFormData };
        }
    }

    renderedCallback() {
        // Set select values after render
        this.updateSelectValues();
    }

    updateSelectValues() {
        const requestTypeSelect = this.template.querySelector('#requestType');
        const contactMethodSelect = this.template.querySelector('#contactMethod');

        if (requestTypeSelect && this.formData.requestType) {
            requestTypeSelect.value = this.formData.requestType;
        }

        if (contactMethodSelect && this.formData.contactMethod) {
            contactMethodSelect.value = this.formData.contactMethod;
        }
    }

    get isSubmitDisabled() {
        return !this.formData.name || 
               !this.formData.email || 
               !this.formData.phone || 
               !this.formData.requestType || 
               !this.formData.contactMethod || 
               !this.formData.privacyConsent;
    }

    handleBackdropClick() {
        this.handleCloseModal();
    }

    handleModalClick(event) {
        // Prevent modal from closing when clicking inside the modal content
        event.stopPropagation();
    }

    handleCloseModal() {
        this.resetForm();
        this.dispatchEvent(new CustomEvent('close'));
    }

    handleInputChange(event) {
        let field;
        let value;

        if (event.detail && event.detail.name) {
            // Handle op-input components
            field = event.detail.name;
            value = event.detail.value;
        } else if (event.target.dataset.field) {
            // Handle regular HTML inputs
            field = event.target.dataset.field;
            value = event.target.value;
        } else if (event.target.name) {
            // Handle inputs with name attribute
            field = event.target.name;
            value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
        }

        if (field) {
            this.formData = {
                ...this.formData,
                [field]: value
            };
        }

        console.log('Form data updated:', this.formData);
    }

    handleCheckboxChange(event) {
        this.formData = {
            ...this.formData,
            privacyConsent: event.detail.checked
        };
        console.log('Privacy consent updated:', this.formData.privacyConsent);
    }

    handleSubmit() {
        // Validate required fields
        if (!this.formData.name || !this.formData.email || !this.formData.phone) {
            alert('Please fill in all required fields (Name, Email, Contact number)');
            return;
        }

        if (!this.formData.requestType || !this.formData.contactMethod) {
            alert('Please select both request type and contact method');
            return;
        }

        if (!this.formData.privacyConsent) {
            alert('Please acknowledge the privacy statement to continue');
            return;
        }

        console.log('Form submitted:', this.formData);

        // Dispatch submit event with form data
        this.dispatchEvent(new CustomEvent('submit', {
            detail: this.formData
        }));
    }

    resetForm() {
        this.formData = {
            name: '',
            email: '',
            phone: '',
            solution: '',
            requestType: '',
            contactMethod: '',
            message: '',
            privacyConsent: false
        };
    }

    @api
    setFormData(data) {
        this.formData = { ...this.formData, ...data };
    }

    @api
    getFormData() {
        return { ...this.formData };
    }
}
