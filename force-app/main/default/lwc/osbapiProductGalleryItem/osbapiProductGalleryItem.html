<template >
    <main>
      <op-card
        lwc:external
        title={title}
        elevated
        style="width: 308px; height: 256px; min-height: 256px; max-height: 256px; display: flex; flex-direction: column;"
      >
        <div
          slot="icon"
          style="border-radius: 50%;  display: flex; align-items: center; justify-content: center;"
        >
          <template if:true={hasIcon}>
                <img src={icon} class="logoSolution" />
            </template>
            <template if:false={hasIcon}>
                <img src={SBLogo} class="logoSolution" />
            </template>
        </div>
        <p style="
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 4;
            line-clamp: 4;
            -webkit-box-orient: vertical;
            margin: 12px 0;
            min-height: 80px;
        ">{description}</p>
        <div slot="footer" style="display: flex; justify-content: space-between; width:100%; ">
            <op-button lwc:external dense =true kind="tertiary" onclick={handleGetInTouchClick}>GET IN TOUCH</op-button>
            <op-button lwc:external dense =true kind="tertiary" onclick={handleNavigation}>READ MORE</op-button>
        </div>
      </op-card>
    </main>
        <c-osb-get-in-touch-modal
        show={showGetInTouchModal}
        onclose={handleCloseModal}
        onsubmit={handleModalSubmit}>
    </c-osb-get-in-touch-modal>
  </template>