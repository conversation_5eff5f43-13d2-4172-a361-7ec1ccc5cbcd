import { LightningElement, api, wire, track } from "lwc";
import { NavigationMixin } from "lightning/navigation";
import { publish, MessageContext } from "lightning/messageService";
import eventChannel from "@salesforce/messageChannel/osbMenuEvents__c";
import OSB_Logo from "@salesforce/resourceUrl/OSB_logoBadge";

export default class OsbapiProductGalleryItem extends NavigationMixin(
    LightningElement
) {
    SBLogo = OSB_Logo;

    @api title;
    @api description;
    @api recordId;
    @api icon = "";
    solTM = false;
    destinationUrl;
    hasIcon = false;
    cssStyle;

    @track showGetInTouchModal = false;

    @wire(MessageContext)
    messageContext;
    handleProductInformation() {
        const payload = {
            ComponentName: "api product catalogue",
            Details: {
                productId: this.recordId
            }
        };
        publish(this.messageContext, eventChannel, payload);
    }

    navigateToDetailsPage() {
        this[NavigationMixin.Navigate]({
            type: "standard__recordPage",
            attributes: {
                recordId: this.recordId,
                objectApiName: "CommunityApi__c",
                actionName: "view"
            },
            state: {
                tab: "OneDeveloper",
                heading: this.title
            }
        });
    }

    handleNavigation() {
        this.handleProductInformation();
        this.navigateToDetailsPage();
    }

    setCssClass() {
        if (this.icon.length !== 0) {
            this.hasIcon = true;
        } else {
            this.hasIcon = false;
        }
    }

    connectedCallback() {
        this.setCssClass();
    }
    navigateContactUs() {
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: 'OPTL_Contact_Us__c'
            }
        });
    }

    handleGetInTouchClick() {
        this.showGetInTouchModal = true;
    }

    handleCloseModal() {
        this.showGetInTouchModal = false;
    }

    handleModalSubmit(event) {
        const formData = event.detail;
        console.log('Form submitted:', formData);
        this.handleCloseModal();
    }

}