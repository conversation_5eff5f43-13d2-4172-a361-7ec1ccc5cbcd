<template>
    <div class="splash-page-header-background">
        <img src={headerBackground} alt="Shield background">
    </div>
    <div class="splash-page-wrapper">
        <div class="splash-page-header">
            <c-osb-detail-product-card
                image={image}
                heading={apiHeading}
                subheading={apiSubheading}
                button-label="Get in Touch">
            </c-osb-detail-product-card>
        </div>
        <div class="splash-page-value-prop">
            <c-osb-value-propositions
                heading={apiHeading}
                value-propositions={benefitsList}>
            </c-osb-value-propositions>
        </div>
        <div class="splash-page-cta-section">
            <c-osbapi-api-catalogue
                product-id={recordId}>
            </c-osbapi-api-catalogue>
        </div>
        <template lwc:if={tabObject}>
            <div class="splash-pagetab-section">
                <c-osb-splash-tab-body
                    data={tabObject}>
                </c-osb-splash-tab-body>
            </div>
        </template>
    </div>

    <!-- Get in Touch Modal -->
    <template lwc:if={showGetInTouchModal}>
        <div class="modal-backdrop" onclick={handleCloseModal}>
            <div class="get-in-touch-modal" onclick={handleModalClick}>
                <div class="modal-header">
                    <h2>Get in touch</h2>
                    <button class="close-button" onclick={handleCloseModal}>
                        <lightning-icon icon-name="utility:close" size="small"></lightning-icon>
                    </button>
                </div>
                <div class="modal-content">
                    <p class="modal-description">
                        Once you submit this form, the API will be automatically added to your dashboard.
                    </p>

                    <form class="contact-form">
                        <div class="form-group">
                            <label for="nameInput">Name and Surname</label>
                            <input
                                id="nameInput"
                                type="text"
                                placeholder="John Dlamini"
                                value={formData.name}
                                onchange={handleInputChange}
                                data-field="name"
                            />
                        </div>

                        <div class="form-group">
                            <label for="emailInput">Email</label>
                            <input
                                id="emailInput"
                                type="email"
                                placeholder="<EMAIL>"
                                value={formData.email}
                                onchange={handleInputChange}
                                data-field="email"
                            />
                        </div>

                        <div class="form-group">
                            <label for="phoneInput">Contact number</label>
                            <input
                                id="phoneInput"
                                type="tel"
                                placeholder="+27 724355032"
                                value={formData.phone}
                                onchange={handleInputChange}
                                data-field="phone"
                            />
                        </div>

                        <div class="form-group">
                            <label for="solutionInput">Solution name</label>
                            <input
                                id="solutionInput"
                                type="text"
                                placeholder="Authentifi"
                                value={formData.solution}
                                onchange={handleInputChange}
                                data-field="solution"
                            />
                        </div>

                        <div class="form-group">
                            <label for="requestType">*Type of request</label>
                            <select
                                id="requestType"
                                onchange={handleInputChange}
                                data-field="requestType"
                            >
                                <option value="">Get a call back</option>
                                <option value="demo">Request a demo</option>
                                <option value="info">More information</option>
                                <option value="support">Technical support</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="contactMethod">*Preferred contact method</label>
                            <select
                                id="contactMethod"
                                onchange={handleInputChange}
                                data-field="contactMethod"
                            >
                                <option value="">Select an option</option>
                                <option value="phone">Phone</option>
                                <option value="email">Email</option>
                                <option value="whatsapp">WhatsApp</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="messageInput">Tell us more about how you would like to use this solution</label>
                            <textarea
                                id="messageInput"
                                placeholder="Placeholder"
                                onchange={handleInputChange}
                                data-field="message"
                                rows="4"
                            >{formData.message}</textarea>
                        </div>

                        <div class="form-group checkbox-group">
                            <input
                                type="checkbox"
                                id="privacyConsent"
                                checked={formData.privacyConsent}
                                onchange={handleInputChange}
                                data-field="privacyConsent"
                            />
                            <label for="privacyConsent" class="checkbox-label">
                                I acknowledge and consent to the processing of my personal information by Standard Bank and/or relevant third-party providers, in line with Standard Bank's Privacy Statement.
                            </label>
                        </div>

                        <button type="button" class="submit-button" onclick={handleSubmit}>
                            SUBMIT
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </template>
</template>